import arrow
import datetime
import calendar
from typing import List, Dict, Any

from apps.models.common import ObjectIdStr
import apps.models.prod_count as PCModel
from apps.db import MongoDB
from apps.common.app_exc import AppException
from apps.common.http_base import HttpResp


# 数据库集合
prod_count_log = MongoDB.get_collection("prod_count_log")
prod_count_config = MongoDB.get_collection("prod_count_config")


async def report_event(data: PCModel.ProdCountEvent) -> Dict[str, Any]:
    """事件上报业务逻辑"""
    # 使用model_dump获取字典后添加receive_timestamp字段
    data_dict = data.model_dump()
    data_dict["receive_timestamp"] = arrow.now().datetime
    await prod_count_log.insert_one(data_dict)
    return {}


async def get_vehicle_config(vehicle_id: ObjectIdStr) -> Dict[str, Any]:
    """获取车辆配置业务逻辑"""
    config = await prod_count_config.find_one(
        {"vehicle_id": ObjectIdStr(vehicle_id)},
        {"_id": 0, "vehicle_id": 0}
    )
    return config or {}


async def set_vehicle_config(vehicle_id: ObjectIdStr, data: PCModel.ProdCountConfig) -> Dict[str, Any]:
    """设置车辆配置业务逻辑"""
    await prod_count_config.update_one(
        {"vehicle_id": ObjectIdStr(vehicle_id)},
        {"$set": data.model_dump()},
        upsert=True,
    )
    return {}


async def get_history_load(q: PCModel.Query) -> PCModel.HistoryLoadOut:
    """获取历史装车记录业务逻辑"""
    skip = (q.page_no - 1) * q.page_size
    query_filter = {"event": {"$in": ["finish", "replenish"]}}
    if q.vehicle_id:
        query_filter["vehicle_id"] = q.vehicle_id

    count = await prod_count_log.count_documents(query_filter)
    if count == 0:
        return PCModel.HistoryLoadOut(count=0, lists=[])

    cursor = prod_count_log.find(query_filter).sort("timestamp", -1).skip(skip).limit(q.page_size)
    history_load = []
    async for doc in cursor:
        history_load.append(PCModel.ProdCountEventOut(**doc))
    return PCModel.HistoryLoadOut(count=count, lists=history_load)


async def _get_team_records(vehicle_id: ObjectIdStr, date_str: str) -> List[PCModel.TeamRecordOut]:
    """获取班组记录的辅助函数"""
    try:
        start_of_day = arrow.get(date_str, "YYYY-MM-DD").to("Asia/Shanghai").floor("day")
        end_of_day = start_of_day.ceil("day")
        start_timestamp = int(start_of_day.timestamp() * 1000)
        end_timestamp = int(end_of_day.timestamp() * 1000)
    except arrow.parser.ParserError:
        raise AppException(HttpResp.TIME_FORMAT_ERROR)

    match_filter = {
        "vehicle_id": vehicle_id,
        "event":  {"$in": ["finish", "replenish"]},
        "timestamp": {"$gte": start_timestamp, "$lt": end_timestamp},
    }

    # 统计班组配置数量
    group_count_pipeline = [
        {"$match": match_filter},
        {"$group": {"_id": "$group_name"}},
        {"$count": "group_count"},
    ]
    group_count_result = await prod_count_log.aggregate(group_count_pipeline).to_list(length=1)
    group_config_count = group_count_result[0]["group_count"] if group_count_result else 0

    project_stage = {
        "_id": 0,
        "group_name": 1,
        "driver_name": 1,
        "vehicle_name": 1,
        "latest_timestamp": 1,
        "count": 1,
    }

    if group_config_count <= 1:
        # 单班组情况的聚合管道
        pipeline = [
            {"$match": match_filter},
            {"$sort": {"timestamp": -1}},
            {
                "$group": {
                    "_id": {
                        "$dateToString": {
                            "format": "%Y-%m-%d",
                            "date": {"$toDate": "$timestamp"},
                            "timezone": "Asia/Shanghai",
                        }
                    },
                    "count": {"$sum": 1},
                    "group_name": {"$first": "$group_name"},
                    "driver_name": {"$first": "$driver_name"},
                    "vehicle_name": {"$first": "$vehicle_name"},
                    "latest_timestamp": {"$max": "$timestamp"},
                }
            },
            {"$sort": {"latest_timestamp": -1}},
            {"$project": project_stage},
        ]
    else:
        # 多班组情况的聚合管道
        group_stage = {
            "_id": {"group_id": "$group_id"},
            "group_name": {"$first": "$group_name"},
            "driver_name": {"$first": "$driver_name"},
            "vehicle_name": {"$first": "$vehicle_name"},
            "count": {"$sum": 1},
            "latest_timestamp": {"$max": "$timestamp"},
        }

        pipeline = [
            {"$match": match_filter},
            {"$sort": {"timestamp": 1}},
            {
                "$setWindowFields": {
                    "sortBy": {"timestamp": 1},
                    "output": {"prev_group_name": {"$shift": {"output": "$group_name", "by": -1, "default": None}}},
                }
            },
            {"$addFields": {"is_new_group": {"$cond": [{"$ne": ["$group_name", "$prev_group_name"]}, 1, 0]}}},
            {
                "$setWindowFields": {
                    "sortBy": {"timestamp": 1},
                    "output": {
                        "group_id": {
                            "$sum": "$is_new_group",
                            "window": {"documents": ["unbounded", "current"]},
                        }
                    },
                }
            },
            {"$group": group_stage},
            {"$sort": {"latest_timestamp": -1}},
            {"$project": project_stage},
        ]

    res = []
    async for doc in prod_count_log.aggregate(pipeline):
        if "_id" in doc:
            doc["_id"] = str(doc["_id"])
        res.append(PCModel.TeamRecordOut(**doc))
    return res


async def get_team_records_by_date_range(vehicle_id: ObjectIdStr, start_time: str, end_time: str) -> List[Dict[str, Any]]:
    """获取指定日期范围内的班组记录，并合并跨天记录"""
    start_day = arrow.get(start_time).to("Asia/Shanghai").floor("day")
    end_day = arrow.get(end_time).to("Asia/Shanghai").floor("day")

    all_merged_records = []
    last_day_last_record = None

    current_day = start_day
    while current_day <= end_day:
        date_str = current_day.format("YYYY-MM-DD")
        team_records = await _get_team_records(vehicle_id, date_str)

        if team_records and len(team_records) > 1:
            sorted_records = sorted(team_records, key=lambda r: r.latest_timestamp)

            # 与前一天的最后一条记录合并
            if last_day_last_record and sorted_records:
                first_record_of_day = sorted_records[0]
                if last_day_last_record['group_name'] == first_record_of_day.group_name and \
                   last_day_last_record['driver_name'] == first_record_of_day.driver_name:
                    last_day_last_record['count'] += first_record_of_day.count
                    last_day_last_record['latest_timestamp'] = first_record_of_day.latest_timestamp
                    sorted_records.pop(0)

            # 在内存中合并当天剩余的记录
            merged_records = []
            if sorted_records:
                # 将 Pydantic 模型转换为字典
                current_record = sorted_records.pop(0).model_dump()
                current_record['vehicle_id'] = vehicle_id

                while sorted_records:
                    next_record = sorted_records.pop(0)
                    if current_record['group_name'] == next_record.group_name and \
                       current_record['driver_name'] == next_record.driver_name:
                        current_record['count'] += next_record.count
                        current_record['latest_timestamp'] = next_record.latest_timestamp
                    else:
                        merged_records.append(current_record)
                        current_record = next_record.model_dump()
                        current_record['vehicle_id'] = vehicle_id
                merged_records.append(current_record)

            if merged_records:
                # 如果上一个记录存在，则将第一个新合并的记录与上一个记录合并
                if last_day_last_record and all_merged_records:
                    if all_merged_records[-1]['group_name'] == merged_records[0]['group_name'] and \
                       all_merged_records[-1]['driver_name'] == merged_records[0]['driver_name']:
                        all_merged_records[-1]['count'] += merged_records[0]['count']
                        all_merged_records[-1]['latest_timestamp'] = merged_records[0]['latest_timestamp']
                        merged_records.pop(0)

                all_merged_records.extend(merged_records)

                if all_merged_records:
                  last_day_last_record = all_merged_records[-1]
        elif team_records and len(team_records) == 1:
            single_record = team_records[0]
            record_dict = single_record.model_dump()
            record_dict['vehicle_id'] = vehicle_id
            all_merged_records.append(record_dict)
            last_day_last_record = record_dict

        current_day = current_day.shift(days=1)

    return all_merged_records


async def get_recent_team(vehicle_id: ObjectIdStr) -> Dict[str, Any]:
    """获取最近班组记录业务逻辑"""
    now = arrow.now("Asia/Shanghai")
    start_time = now.shift(days=-2).format("YYYY-MM-DD")
    end_time = now.format("YYYY-MM-DD")
    all_records = await get_team_records_by_date_range(vehicle_id, start_time, end_time)
    # 返回最新的3条记录
    recent_records = all_records[:3]
    return {
        "count": len(recent_records),
        "lists": recent_records
    }


async def get_history_team(q: PCModel.HistoryTeamQuery) -> Dict[str, Any]:
    """获取历史班组记录业务逻辑"""
    if not q.vehicle_id:
        raise AppException(HttpResp.PARAM_INVALID)

    now = arrow.now("Asia/Shanghai")
    start_time = q.start_time or now.shift(days=-6).format("YYYY-MM-DD")
    end_time = q.end_time or now.format("YYYY-MM-DD")

    records = await get_team_records_by_date_range(q.vehicle_id, start_time, end_time)

    # 手动分页
    total = len(records)
    start_index = (q.page_no - 1) * q.page_size
    end_index = start_index + q.page_size
    paginated_records = records[start_index:end_index]

    return {
        "count": total,
        "lists": paginated_records,
    }


async def _calculate_avg_load_time(vehicle_id: ObjectIdStr, start_time: int, end_time: int) -> float:
    """计算指定时间范围内的平均装车时间"""
    pipeline = [
        {
            "$match": {
                "vehicle_id": vehicle_id,
                "timestamp": {"$gte": start_time, "$lt": end_time},
                "event":  {"$in": ["finish", "replenish"]},
            }
        },
        {"$sort": {"timestamp": 1}},
        {"$project": {"_id": 0, "timestamp": 1}},
    ]

    timestamps = [doc["timestamp"] async for doc in prod_count_log.aggregate(pipeline)]

    if len(timestamps) < 2:
        return 0.0

    total_duration = 0
    count_pairs = 0

    for i in range(1, len(timestamps)):
        duration = timestamps[i] - timestamps[i - 1]

        if 1 * 60 * 1000 <= duration < 120 * 60 * 1000:  # 1 minute to 2 hours
            total_duration += duration
            count_pairs += 1

    if count_pairs > 0:
        return (total_duration / count_pairs) / 60 / 1000  # in minutes
    return 0.0


async def get_daily_stats(date: str, vehicle_id: ObjectIdStr) -> Dict[str, Any]:
    """按日统计业务逻辑"""
    # 获取车辆配置中的装车阈值
    config = await prod_count_config.find_one({"vehicle_id": vehicle_id})
    if config and "load_threshold" in config:
        threshold_minutes = config["load_threshold"]
    else:
        threshold_minutes = 60  # 默认值

    try:
        # 将日期字符串解析为datetime对象，并设置时间为当天的开始（00:00:00）
        start_of_day_dt = datetime.datetime.strptime(date, "%Y-%m-%d").replace(hour=0, minute=0, second=0, microsecond=0)
        print(date, start_of_day_dt)
    except ValueError:
        # 如果日期格式无效，则引发AppException
        raise AppException(HttpResp.TIME_FORMAT_ERROR)

    # 计算当天的结束时间（即第二天的开始）
    end_of_day_dt = start_of_day_dt + datetime.timedelta(days=1)
    # 将开始时间转换为毫秒级时间戳
    start_timestamp = int(start_of_day_dt.timestamp() * 1000)
    # 将结束时间转换为毫秒级时间戳
    end_timestamp = int(end_of_day_dt.timestamp() * 1000)

    # 筛选当天所有事件
    match_filter = {
        "vehicle_id": vehicle_id,
        "timestamp": {"$gte": start_timestamp, "$lt": end_timestamp},
    }

    events_cursor = prod_count_log.find(match_filter).sort("timestamp", 1)
    events = await events_cursor.to_list(length=None)

    # 如果没有事件，直接返回空统计
    if not events:
        return {
            "total_loads": 0,
            "avg_load_time": 0,
            "hourly_counts": [0] * 24,
            "normal_work_time_minutes": 0,
            "trouble_shooting_time_minutes": 0,
            "idle_time_minutes": 24 * 60,
        }

    # 计算故障检修时间
    trouble_shooting_time_minutes = 0
    for event in events:
        if event.get("event") == "trouble" and event.get("start_time") and event.get("end_time"):
            try:
                start_h, start_m = map(int, event["start_time"].split(':'))
                end_h, end_m = map(int, event["end_time"].split(':'))
                start_total_minutes = start_h * 60 + start_m
                end_total_minutes = end_h * 60 + end_m
                duration = end_total_minutes - start_total_minutes
                if duration < 0: # 跨天事件处理
                    duration += 24 * 60
                trouble_shooting_time_minutes += duration
            except (ValueError, KeyError):
                continue

    # 筛选"完成装车"事件用于后续计算
    finish_events = [event for event in events if event.get("event") in ["finish", "replenish"]]

    # 计算正常工作时间
    normal_work_time_minutes = 0.0
    if len(finish_events) > 1:
        for i in range(1, len(finish_events)):
            prev_event_ts = finish_events[i-1]["timestamp"]
            curr_event_ts = finish_events[i]["timestamp"]
            time_diff_ms = curr_event_ts - prev_event_ts
            time_diff_minutes = time_diff_ms / (1000 * 60)
            if time_diff_minutes < threshold_minutes:
                 normal_work_time_minutes += time_diff_minutes

    # 计算闲置时间
    total_minutes_in_day = 24 * 60
    calculated_time = normal_work_time_minutes + trouble_shooting_time_minutes
    idle_time_minutes = max(0, total_minutes_in_day - calculated_time)

    # 计算总装车数和每小时分布
    total_loads = len(finish_events)
    hourly_counts = [0] * 24
    for event in finish_events:
        event_time = datetime.datetime.fromtimestamp(event["timestamp"] / 1000)
        hourly_counts[event_time.hour] += 1

    # 计算平均装车时间
    avg_load_time = await _calculate_avg_load_time(vehicle_id, start_timestamp, end_timestamp)

    # 计算班组信息
    team_info = await get_team_records_by_date_range(vehicle_id, start_timestamp, start_timestamp)

    # 以时间段的形式记录今天事件(工作,检修)
    timeline_data = []
    # 1. 添加正常工作时间段
    if len(finish_events) > 1:
        for i in range(1, len(finish_events)):
            prev_event_ts = finish_events[i - 1]["timestamp"]
            curr_event_ts = finish_events[i]["timestamp"]
            time_diff_ms = curr_event_ts - prev_event_ts
            time_diff_minutes = time_diff_ms / (1000 * 60)
            if time_diff_minutes < threshold_minutes:
                start_time_str = datetime.datetime.fromtimestamp(prev_event_ts / 1000).strftime('%H:%M')
                end_time_str = datetime.datetime.fromtimestamp(curr_event_ts / 1000).strftime('%H:%M')

                # 合并逻辑
                if timeline_data and \
                   timeline_data[-1]["name"] == "正常工作" and \
                   timeline_data[-1]["end"] == start_time_str:
                    timeline_data[-1]["end"] = end_time_str
                else:
                    timeline_data.append({
                        "name": "正常工作",
                        "start": start_time_str,
                        "end": end_time_str,
                        "color": "#91cc75"
                    })

    # 2. 添加故障检修时间段
    for event in events:
        if event.get("event") == "trouble" and event.get("start_time") and event.get("end_time"):
            timeline_data.append({
                "name": "故障检修",
                "start": event["start_time"],
                "end": event["end_time"],
                "color": "#EE6666"
            })

    return {
        "total_loads": total_loads,
        "avg_load_time": round(avg_load_time, 2),
        "hourly_counts": hourly_counts,
        "normal_work_time_minutes": round(normal_work_time_minutes),
        "trouble_shooting_time_minutes": round(trouble_shooting_time_minutes),
        "idle_time_minutes": round(idle_time_minutes),
        "team_info": team_info,
        "timeline_data": timeline_data,
    }


async def get_monthly_stats(month: str, vehicle_id: ObjectIdStr) -> Dict[str, Any]:
    """按月统计业务逻辑"""
    year, mon = map(int, month.split("-"))
    _, num_days = calendar.monthrange(year, mon)
    start_of_month = int(datetime.datetime(year, mon, 1).timestamp() * 1000)
    end_of_month = int(datetime.datetime(year, mon, num_days, 23, 59, 59).timestamp() * 1000)

    match_filter = {
        "vehicle_id": vehicle_id,
        "timestamp": {"$gte": start_of_month, "$lt": end_of_month},
        "event": {"$in": ["finish", "replenish"]},
    }

    pipeline = [
        {"$match": match_filter},
        {
            "$group": {
                "_id": {"$dayOfMonth": {"$toDate": {"$multiply": ["$timestamp", 1000]}}},
                "count": {"$sum": 1},
            }
        },
        {"$sort": {"_id": 1}},
    ]

    daily_counts = [0] * num_days
    total_loads = 0
    cursor = prod_count_log.aggregate(pipeline)
    async for doc in cursor:
        day = doc["_id"]
        if 1 <= day <= num_days:
            daily_counts[day - 1] = doc["count"]
            total_loads += doc["count"]

    avg_load_time = await _calculate_avg_load_time(vehicle_id, start_of_month, end_of_month)

    if total_loads > 0:
        avg_daily_loads = total_loads / num_days
    else:
        avg_daily_loads = 0

    # 计算班组信息
    start_of_month_str = datetime.datetime.fromtimestamp(start_of_month / 1000).strftime("%Y-%m-%d")
    end_of_month_str = datetime.datetime.fromtimestamp(end_of_month / 1000).strftime("%Y-%m-%d")

    team_info = await get_team_records_by_date_range(vehicle_id, start_of_month_str, end_of_month_str)

    return {
        "total_loads": total_loads,
        "avg_load_time": round(avg_load_time, 2),
        "avg_daily_loads": round(avg_daily_loads, 2),
        "daily_counts": daily_counts,
        "days_in_month": num_days,
        "team_info": team_info,
    }
